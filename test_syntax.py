#!/usr/bin/env python3
"""
Simple syntax test for Missing_Log_Imputation_Petrel_Interactive.py
"""

import sys
import traceback

def test_import():
    """Test if the module can be imported without errors"""
    try:
        # Test basic imports first
        import numpy as np
        import pandas as pd
        print("✓ Basic imports successful")
        
        # Test sklearn imports
        from sklearn.linear_model import LinearRegression
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import mean_absolute_error
        from sklearn.preprocessing import StandardScaler
        from sklearn.impute import SimpleImputer
        print("✓ Sklearn imports successful")
        
        # Test if the main script can be imported (syntax check)
        print("Testing main script import...")
        
        # Mock the parameters to avoid Petrel connection
        import Missing_Log_Imputation_Petrel_Interactive as main_script
        print("✓ Main script imported successfully")
        
        # Test class instantiation without Petrel
        print("Testing class definitions...")
        
        # This should work without Petrel connection
        try:
            framework = main_script.PetrelMLImputationFramework(
                enable_advanced_models=False,
                enable_tpot=False
            )
            print("✓ PetrelMLImputationFramework instantiated successfully")
        except Exception as e:
            print(f"✗ Error instantiating PetrelMLImputationFramework: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Import error: {e}")
        traceback.print_exc()
        return False

def test_data_processing():
    """Test data processing functions with mock data"""
    try:
        import numpy as np
        import pandas as pd
        from sklearn.impute import SimpleImputer
        from sklearn.linear_model import LinearRegression
        
        # Create mock data similar to what the script expects
        mock_data = pd.DataFrame({
            'MD': np.arange(100, 200, 1),
            'WELL': ['TestWell'] * 100,
            'DEN_COREL': np.random.normal(2.5, 0.2, 100),
            'RT_COREL': np.random.normal(10, 2, 100),
            'GR_COREL': np.random.normal(50, 15, 100),
            'VP_COREL_ML': np.random.normal(3000, 300, 100)
        })
        
        # Add some missing values
        mock_data.loc[80:90, 'VP_COREL_ML'] = np.nan
        
        print("✓ Mock data created successfully")
        print(f"  Data shape: {mock_data.shape}")
        print(f"  Columns: {list(mock_data.columns)}")
        print(f"  Missing values in target: {mock_data['VP_COREL_ML'].isna().sum()}")
        
        # Test the imputation functions
        import Missing_Log_Imputation_Petrel_Interactive as main_script
        
        feature_cols = ['MD', 'DEN_COREL', 'RT_COREL', 'GR_COREL']
        target_col = 'VP_COREL_ML'
        
        # Test model training preparation
        complete_mask = mock_data[target_col].notna()
        train_data = mock_data[complete_mask].copy()
        
        X = train_data[feature_cols].copy()
        y = train_data[target_col].values
        
        # Test imputation
        imputer = SimpleImputer(strategy='mean')
        X_imputed = imputer.fit_transform(X)
        
        # Test model training
        model = LinearRegression()
        model.fit(X_imputed, y)
        
        print("✓ Basic ML pipeline test successful")
        
        # Test the actual imputation function
        try:
            result = main_script.perform_imputation(
                mock_data, target_col, feature_cols, model, imputer
            )
            print("✓ perform_imputation function test successful")
            print(f"  Result shape: {result.shape}")
            print(f"  Missing values after imputation: {result[target_col].isna().sum()}")
        except Exception as e:
            print(f"✗ perform_imputation function test failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Data processing test error: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("="*60)
    print("SYNTAX AND FUNCTIONALITY TEST")
    print("="*60)
    
    success = True
    
    print("\n1. Testing imports...")
    success &= test_import()
    
    print("\n2. Testing data processing...")
    success &= test_data_processing()
    
    print("\n" + "="*60)
    if success:
        print("✓ ALL TESTS PASSED")
        print("The script appears to be syntactically correct and functional.")
    else:
        print("✗ SOME TESTS FAILED")
        print("There are issues that need to be addressed.")
    print("="*60)
    
    sys.exit(0 if success else 1)
